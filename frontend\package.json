{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@redux-devtools/extension": "^3.3.0", "axios": "^1.9.0", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "react-scroll": "^1.9.3", "react-select": "^5.10.1", "react-tooltip": "^5.28.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.3.2"}}