{"name": "react-loading-skeleton", "version": "3.5.0", "description": "Make beautiful, animated loading skeletons that automatically adapt to your app.", "keywords": ["react", "loading", "skeleton", "progress", "spinner"], "repository": {"type": "git", "url": "https://github.com/dvtng/react-loading-skeleton.git"}, "license": "MIT", "author": "<PERSON>", "sideEffects": ["**/*.css"], "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "./dist/skeleton.css": "./dist/skeleton.css"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"build": "yarn clean && tsc && rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "lint-staged": "lint-staged --no-stash", "prepack": "yarn run build", "prettier-all": "prettier . --write", "setup": "husky install", "storybook": "storybook dev -p 8080", "test": "vitest"}, "lint-staged": {"src/**/*.ts?(x)": ["eslint --max-warnings 0 --fix", "prettier --write"], "*.{md,js,cjs,yml,json}": "prettier --write", "vite.config.ts": "prettier --write", ".storybook/*.ts": "prettier --write"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.0", "@storybook/addon-essentials": "^7.0.7", "@storybook/addon-interactions": "^7.0.7", "@storybook/addon-links": "^7.0.7", "@storybook/blocks": "^7.0.7", "@storybook/react": "^7.0.7", "@storybook/react-vite": "^7.0.7", "@swc/core": "^1.3.56", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.1", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "@vitejs/plugin-react-swc": "^3.3.0", "eslint": "^8.39.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^5.10.3", "husky": "^8.0.3", "jsdom": "^21.1.1", "lint-staged": "^13.2.2", "prettier": "^2.8.8", "prettier-plugin-packagejson": "^2.4.3", "prop-types": "^15.8.1", "react": "^17.0.2", "react-dom": "^17.0.2", "rimraf": "^5.0.0", "rollup": "^3.21.1", "rollup-plugin-copy": "^3.4.0", "storybook": "^7.0.7", "tslib": "^2.5.0", "typescript": "^5.0.4", "vite": "^4.3.3", "vite-plugin-checker": "^0.5.6", "vitest": "^0.30.1"}, "peerDependencies": {"react": ">=16.8.0"}, "packageManager": "yarn@3.5.0"}