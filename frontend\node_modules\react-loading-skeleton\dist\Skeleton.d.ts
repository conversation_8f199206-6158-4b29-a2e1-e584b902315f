import React, { CSSProperties, PropsWithChildren, ReactElement } from 'react';
import { SkeletonStyleProps } from './SkeletonStyleProps.js';
export interface SkeletonProps extends SkeletonStyleProps {
    count?: number;
    wrapper?: React.FunctionComponent<PropsWithChildren<unknown>>;
    className?: string;
    containerClassName?: string;
    containerTestId?: string;
    circle?: boolean;
    style?: CSSProperties;
}
export declare function Skeleton({ count, wrapper: Wrapper, className: customClassName, containerClassName, containerTestId, circle, style: styleProp, ...originalPropsStyleOptions }: SkeletonProps): ReactElement;
